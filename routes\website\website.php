<?php
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use App\Http\Controllers\website\website;


//  Route::get('/', function () {
//      return view('website.comingSoon'); 
//  });

 Route::get('/', [website::class, 'HomeView']);
Route::get('/home', [website::class, 'HomeView']);

Route::get('/about', [website::class, 'AboutView']);

Route::get('/404', function () {
    return view('website.404'); 
});

Route::get('/contact', function () {
    return view('website.contact'); 
});

Route::get('/projects', function () {
    return view('website.projects'); 
});
Route::get('/blog', [website::class, 'blogView']);

Route::get('/blog/{slug}', [website::class, 'singleBlogView'])->name('blog.detail');

// services routes

Route::get('/products', function () {
    return view('website.products'); 
});



// Product Detail Pages Routes

// Bed Products
Route::get('/single-bed', function () {
    return view('website.singleBed');
});

Route::get('/double-bed', function () {
    return view('website.doubleBed');
});

Route::get('/folding-bed', function () {
    return view('website.foldingBed');
});

Route::get('/bed-side-table', function () {
    return view('website.bedSideTable');
});

// Cabinet & Locker Products
Route::get('/file-cabinet', function () {
    return view('website.fileCabinet');
});

Route::get('/steel-locker', function () {
    return view('website.steelLocker');
});

Route::get('/steel-cabinet', function () {
    return view('website.steelCabinet');
});

// Table & Desk Products
Route::get('/office-table', function () {
    return view('website.officeTable');
});

Route::get('/dining-table', function () {
    return view('website.diningTable');
});

Route::get('/study-desk', function () {
    return view('website.studyDesk');
});

Route::get('/coffee-table', function () {
    return view('website.coffeeTable');
});

// Seating Products
Route::get('/steel-sofas', function () {
    return view('website.steelSofas');
});

// Rack & Shelving Products
Route::get('/steel-racks', function () {
    return view('website.steelRacks');
});

Route::get('/shoe-racks', function () {
    return view('website.shoeRacks');
});

// Construction & Industrial Products
Route::get('/wire-mesh', function () {
    return view('website.wireMesh');
});

Route::get('/steel-reinforcing-mesh', function () {
    return view('website.steelReinforcingMesh');
});

Route::get('/welded-wire-mesh', function () {
    return view('website.weldedWireMesh');
});

Route::get('/ladder-mesh', function () {
    return view('website.ladderMesh');
});

Route::get('/fencing-mesh', function () {
    return view('website.fencingMesh');
});

Route::get('/barbed-wire', function () {
    return view('website.barbedWire');
});

Route::get('/wire-nails', function () {
    return view('website.wireNails');
});

// Paint & Coating Products
Route::get('/powder-paint', function () {
    return view('website.powderPaint');
});